package com.sankuai.shangou.logistics.delivery.configure.enums;

/**
 * <AUTHOR>
 * @date 2025-08-26
 * @email <EMAIL>
 */
public enum TimeConfigTypeEnum {

    FIXED(1, "固定时长"),
    ETA(2, "eta加减时长")
    ;

    private final int type;
    private final String desc;
    TimeConfigTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public static TimeConfigTypeEnum valueOfType(int type) {
        for (TimeConfigTypeEnum obj : TimeConfigTypeEnum.values()) {
            if (java.util.Objects.equals(obj.type, type)) {
                return obj;
            }
        }
        return null;
    }


    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
