package com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.consumer;

import com.dianping.cat.Cat;
import com.google.common.base.Preconditions;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.dms.base.model.value.DeliveryChannel;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryChannelApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.DeliveryTransformApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.channel.OcmsChannelClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.order.OrderSystemClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.client.warn.DeliveryWarnClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.log.changeinfo.DeliveryExceptionInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.AssessTimeAppender;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.MccConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.TransferOrderMarkEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.dao.model.PricingRouteInfoDO;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.OFCRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.facade.TenantRemoteService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferMtFamousTavernOrderSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.cache.TransferOrderSquirrelOperateService;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.DapDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.MaltFarmDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.client.platform.MerchantSelfDeliveryPlatformClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DelayNotifyDeliveryStatusMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.message.DeliveryTransSelfMsg;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.mq.producer.MafkaDelayMessageProducer;
import com.sankuai.meituan.shangou.empower.tms.delivery.infrastructure.service.DiscreetDeliveryService;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.PricingRouteInfoRepository;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.RouteInfoDTO;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.StaffRider;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.DeliveryChangeSyncOutMessage;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.sync.RiderDeliveryOrderSyncOutClient;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.facade.RouteFacade;
import com.sankuai.shangou.qnh.ofc.ebase.consts.PickDeliveryWorkModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;

import static com.meituan.mafka.client.consumer.ConsumeStatus.*;

@Service
@Slf4j
public class DeliveryOrderTransMessageListener extends AbstractDeadLetterConsumer{

    @Resource
    private OrderSystemClient orderSystemClient;

    @Resource
    private MaltFarmDeliveryPlatformClient maltFarmDeliveryPlatformClient;

    @Resource
    private DapDeliveryPlatformClient dapDeliveryPlatformClient;

    @Resource
    private MerchantSelfDeliveryPlatformClient merchantSelfDeliveryPlatformClient;

    @Resource
    private DeliveryPoiRepository deliveryPoiRepository;

    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Resource
    private RouteFacade routeFacade;

    @Resource
    private DeliveryChangeNotifyService deliveryChangeNotifyService;

    @Resource
    private TransferOrderSquirrelOperateService transferOrderSquirrelOperateService;

    @Resource
    private DeliveryWarnClient deliveryWarnClient;

    @Resource
    private MafkaDelayMessageProducer<DelayNotifyDeliveryStatusMessage> delayNotifyDeliveryStatusMessageProducer;

    @Resource
    private TenantRemoteService tenantRemoteService;
    @Resource
    private DeliveryTransformApplicationService deliveryTransformApplicationService;

    @Resource
    private OcmsChannelClient ocmsChannelClient;

    @Resource
    private DeliveryChannelApplicationService deliveryChannelApplicationService;

    @Resource
    private AssessTimeAppender assessTimeAppender;

    @Resource
    private PricingRouteInfoRepository pricingRouteInfoRepository;

    @Resource
    private DiscreetDeliveryService discreetDeliveryService;

    @Resource
    private RiderDeliveryOrderSyncOutClient riderDeliveryOrderSyncOutClient;
    @Resource
    private TransferMtFamousTavernOrderSquirrelOperateService transferMtFamousTavernOrderSquirrelOperateService;

    @Resource
    private OFCRemoteService ofcRemoteService;
    @Resource
    private DeliveryDimensionPoiRepository deliveryDimensionPoiRepository;


    @Override
    protected MQConsumerEnum consumerConfig() {
        return MQConsumerEnum.DELIVERY_ORDER_TRANS_CONSUMER;
    }

    @Override
    protected ConsumeStatus consume(MafkaMessage message) {
        log.info("开始消费转单发配送消息: {}", message);
        DeliveryTransSelfMsg msg = translateMessage(message);
        if (msg == null) {
            return CONSUME_SUCCESS;
        }
        Result<OrderInfo> orderQueryResult = orderSystemClient.getOrderInfo(new OrderKey(msg.getTenantId(), msg.getStoreId(), msg.getOrderId()), false);
        if (orderQueryResult.isFail()) {
            log.info("订单查询失败,{}",message);
            return CONSUME_FAILURE;
        }
        OrderInfo orderInfo = orderQueryResult.getInfo();

        // 如果订单已到达终态，不再进行后续逻辑
        if (OrderStatusEnum.CANCELED.getValue() == orderInfo.getOrderStatus() || OrderStatusEnum.COMPLETED.getValue() == orderInfo.getOrderStatus()) {
            log.info("订单[{}]已经取消或已经完成，无需进行后续转自送/聚合配送", msg.getOrderId());
            return CONSUME_SUCCESS;
        }

        //读取缓存中的配送平台
        String key = transferOrderSquirrelOperateService.getKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
        TransferOperateInfo transferOperate = new TransferOperateInfo();
        Optional<TransferOperateInfo> transferOperateInfo = transferOrderSquirrelOperateService.get(key, TransferOperateInfo.class);
        if (!transferOperateInfo.isPresent()) {
            log.error("查询转单配送平台为空，放弃转单");
            return CONSUME_FAILURE;
        } else {
            transferOperate = transferOperateInfo.get();
        }
        if (Objects.isNull(transferOperate.getDeliveryPlatformEnum())) {
            log.error("查询转单配送平台为空，放弃转单{}", transferOperate);
            return CONSUME_FAILURE;
        }
        Integer lastDeliveryPlatform = TransDeliveryPlatformEnum.SELF_DELIVERY.getCode();
        //查当前所有运单
        List<DeliveryOrder> deliveryOrders = new ArrayList<>();
        if(MccConfigUtils.getDeliveryQueryTenantSwitch()){
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersMaxWithOrderId(orderInfo.getOrderKey().getOrderId());
        }else {
            deliveryOrders = deliveryOrderRepository.getDeliveryOrdersForceMaster(orderInfo.getOrderKey().getOrderId());
        }

        long distance = 0;
        Long assessDeliveryTime = null;
        DeliveryOrder currentDeliveryOrder = null;
        if(CollectionUtils.isNotEmpty(deliveryOrders)) {
            currentDeliveryOrder = deliveryOrders.stream().filter(new Predicate<DeliveryOrder>() {
                @Override
                public boolean test(DeliveryOrder deliveryOrder) {
                    return deliveryOrder.isActive();
                }
            }).findFirst().orElse(deliveryOrders.stream().sorted(new Comparator<DeliveryOrder>() {
                @Override
                public int compare(DeliveryOrder o1, DeliveryOrder o2) {
                    return o1.getCreateTime().isAfter(o2.getCreateTime()) ? -1:1;
                }
            }).findFirst().get());
            DeliveryChannel deliveryChannel = deliveryChannelApplicationService.queryDeliveryChannelByCarrierCode(currentDeliveryOrder.getDeliveryChannel());
            if (DeliveryChannelEnum.ORDER_PLATFORM_DELIVERY.getCode() == currentDeliveryOrder.getDeliveryChannel()
                    || DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode() == deliveryChannel.getDeliveryPlatFormCode()) {
                lastDeliveryPlatform = TransDeliveryPlatformEnum.ORDER_PLATFORM_DELIVERY.getCode();
            }

            assessDeliveryTime = currentDeliveryOrder.getAssessDeliveryTime();

            //歪马 && 当前运单是三方 && 当前运单状态为0
            boolean noNeedToCancel = noNeedToCancel(currentDeliveryOrder);
            //取消当前运单
            if(!noNeedToCancel) {
                Optional<Failure> cancelFailure = currentDeliveryOrder.cancelForTransOrder();
                if (cancelFailure.isPresent()) {
                    log.warn("订单[{}]转自配送后，取消当前平台配送运单失败，失败原因：{}", orderInfo.getOrderKey(), cancelFailure.get());
                    return CONSUME_FAILURE;
                }
            }
            if(currentDeliveryOrder.isActive() && (currentDeliveryOrder.getStatus() == DeliveryStatusEnum.INIT || currentDeliveryOrder.getStatus() == DeliveryStatusEnum.DELIVERY_LAUNCHED)){
                currentDeliveryOrder.onChange(DeliveryEventEnum.DELIVERY_CANCEL, DeliveryExceptionInfo.NO_EXCEPTION, currentDeliveryOrder.getRiderInfo(), LocalDateTime.now());
            }
        } else if (isProcessLastDeliveryPlatform4OrderPlatformDelivery(orderInfo, msg)) {
            lastDeliveryPlatform = TransDeliveryPlatformEnum.ORDER_PLATFORM_DELIVERY.getCode();
        }
        // 名酒馆&美团&转单之前是平台配送的订单
        if (Objects.equals(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), orderInfo.getOrderBizType()) &&
                !orderInfo.isSelfDelivery() &&
                Boolean.TRUE.equals(orderInfo.getIsMtFamousTavern())) {
            log.info("名酒馆&美团&转单之前是平台配送的订单: orderId: {} channelOrderId:{}", msg.getOrderId(), orderInfo.getChannelOrderId());
            String mtFamousTavernOrderKey = transferMtFamousTavernOrderSquirrelOperateService.getKey(orderInfo.getOrderKey().getTenantId(), orderInfo.getOrderKey().getStoreId(), orderInfo.getOrderKey().getOrderId());
            transferMtFamousTavernOrderSquirrelOperateService.set(mtFamousTavernOrderKey, new MtFamousTavernTransferCacheInfo(msg.getDeliveryPlatformCode()));
            return CONSUME_SUCCESS;
        }

        //常规转单
        int transType = DeliveryOrderTransEnum.NORMAL_TRANS.getTransType();
        if(CollectionUtils.isNotEmpty(deliveryOrders)){
            Optional<DeliveryOrder> optionalDelivery = deliveryOrders.stream().filter(new Predicate<DeliveryOrder>() {
                @Override
                public boolean test(DeliveryOrder deliveryOrder) {
                    return deliveryOrder.getExceptionType() != DeliveryExceptionTypeEnum.NO_EXCEPTION;
                }
            }).findAny();
            if(optionalDelivery.isPresent()){
                //异常转单
                transType = DeliveryOrderTransEnum.DELIVERY_EXCEPTION_TRANS.getTransType();
            }
        }

        DynamicOrderBizType orderBizType = ObjectUtils.defaultIfNull(DynamicOrderBizType.findOf(
                orderInfo.getOrderBizType()), DynamicOrderBizType.MEITUAN_WAIMAI);
        Optional<DeliveryPoi> opDeliveryPoi = deliveryPoiRepository.queryDeliveryPoiWithDefault(orderInfo.getOrderKey().getTenantId(),
                orderInfo.getWarehouseId(), orderBizType.getChannelId());
        if(!opDeliveryPoi.isPresent()){
            log.info("门店查询失败,{}",message);
            return CONSUME_FAILURE;
        }
        //业务线为医药无人仓 商品名称替换货号,是否替换货号由具体仓配置决定
        boolean isMedicineUW = tenantRemoteService.isMedicineUnmannedWarehouse(orderInfo.getOrderKey().getTenantId());
        if(isMedicineUW){
            discreetDeliveryService.replaceMedicineGoodsCode2SkuName(orderInfo.getOrderKey(), orderInfo, orderSystemClient, orderInfo.getWarehouseId());
        }else {
            discreetDeliveryService.replaceNotMedicineUnmannedGoodsCode2SkuName(orderInfo.getOrderKey(), orderInfo, orderSystemClient,
                    orderInfo.getWarehouseId());
        }

        TransferOrderMarkEnum transferOrderMarkEnum = TransferOrderMarkEnum.NORMAL_ORDER;
        if(orderInfo.getOrderTransInfo()!=null && orderInfo.getOrderTransInfo().getDispatchShopId()!=null){
            transferOrderMarkEnum = TransferOrderMarkEnum.TRANSFER_ORDER;
        }

        orderInfo.setSelfDelivery(true);
        DeliveryOrder deliveryOrder=null;
        switch (transferOperate.getDeliveryPlatformEnum()){
            case DAP_DELIVERY_PLATFORM:
                deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, DeliveryChannelEnum.DAP_DELIVERY.getCode(), StringUtils.EMPTY);
                deliveryOrder.setTransType(transType);
                if(currentDeliveryOrder!=null && currentDeliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
                    deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
                    deliveryOrder.setStoreId(currentDeliveryOrder.getStoreId());
                    deliveryOrder.setFulfillmentOrderId(currentDeliveryOrder.getFulfillmentOrderId());
                    deliveryOrder.setOrderKey(new OrderKey(currentDeliveryOrder.getTenantId(),currentDeliveryOrder.getStoreId(),currentDeliveryOrder.getOrderId()));
                }
                deliveryOrder.activate();
                //歪马填充自配的距离
                if (Objects.nonNull(assessDeliveryTime)) {
                    deliveryOrder.setAssessDeliveryTime(assessDeliveryTime);
                }
                deliveryOrderRepository.save(deliveryOrder);

                //新运单创建成功之后发送MQ
                deliveryChangeNotifyService.notifyDrunkHorseTransDeliveryType(orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(),
                        TransDeliveryTypeOperationTypeEnum.TURN_TO_THIRD_DELIVERY, null, null);

                Optional<LaunchFailure> launchFailure = dapDeliveryPlatformClient.launch(opDeliveryPoi.get(), orderInfo, deliveryOrder,transferOrderMarkEnum.getCode());
                if (launchFailure.isPresent()) {
                    deliveryWarnClient.pushTurnToDapFail(orderInfo);
                    Cat.logEvent("TURN_LAUNCH_DAP_FAIL", "FAIL");
                    return RECONSUME_LATER;
                } else {
                    if (Objects.equals(orderInfo.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
                        //微商城才同步TSP转三方消息
                        syncDeliveryPlatformChange(deliveryOrder, DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM);
                    }

                    //发送MQ 触发sharkpush
                    Long oldRiderAccountId = null;
                    if (Objects.nonNull(currentDeliveryOrder)
                            && Objects.equals(currentDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
                            && Objects.nonNull(currentDeliveryOrder.getRiderInfo())
                            && currentDeliveryOrder.getRiderInfo() instanceof StaffRider) {
                        oldRiderAccountId = ((StaffRider) currentDeliveryOrder.getRiderInfo()).getRiderAccountId();
                    }


                    riderDeliveryOrderSyncOutClient.asyncOut(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.TRANS_TO_THIRD_PART_DELIVERY.getValue(),
                            new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderBizType(),
                                    deliveryOrder.getOrderId(), deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
                            new DeliveryChangeSyncOutMessage.TransToThirdPartDeliveryBody(oldRiderAccountId)));
                }
                break;
            case MALT_FARM_DELIVERY_PLATFORM:
                deliveryOrder = DeliveryOrder.fromBusinessCustomer(orderInfo, DeliveryChannelEnum.MALT_FARM.getCode(), StringUtils.EMPTY);
                deliveryOrder.setTransType(transType);
                if(currentDeliveryOrder!=null && currentDeliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
                    deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
                    deliveryOrder.setStoreId(currentDeliveryOrder.getStoreId());
                    deliveryOrder.setFulfillmentOrderId(currentDeliveryOrder.getFulfillmentOrderId());
                    deliveryOrder.setOrderKey(new OrderKey(currentDeliveryOrder.getTenantId(),currentDeliveryOrder.getStoreId(),currentDeliveryOrder.getOrderId()));
                }
                deliveryOrder.activate();
                deliveryOrderRepository.save(deliveryOrder);
                //新运单创建成功之后发送MQ
                deliveryChangeNotifyService.notifyDrunkHorseTransDeliveryType(orderInfo.getChannelOrderId(), orderInfo.getOrderBizType(),
                        TransDeliveryTypeOperationTypeEnum.TURN_TO_THIRD_DELIVERY, null, null);
                Optional<LaunchFailure> launchFailureMal = maltFarmDeliveryPlatformClient.launch(opDeliveryPoi.get(), orderInfo,deliveryOrder,transferOrderMarkEnum.getCode());
                if (launchFailureMal.isPresent()) {
                    deliveryWarnClient.pushTurnToDapFail(orderInfo);
                    Cat.logEvent("TURN_LAUNCH_DAP_FAIL", "FAIL");
                    return RECONSUME_LATER;
                } else {
                    if (Objects.equals(orderInfo.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
                        //微商城才同步TSP转三方消息
                        syncDeliveryPlatformChange(deliveryOrder, DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM);
                    }

                    //发送MQ 触发sharkpush
                    Long oldRiderAccountId = null;
                    if (Objects.nonNull(currentDeliveryOrder)
                            && Objects.equals(currentDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())
                            && Objects.nonNull(currentDeliveryOrder.getRiderInfo())
                            && currentDeliveryOrder.getRiderInfo() instanceof StaffRider) {
                        oldRiderAccountId = ((StaffRider) currentDeliveryOrder.getRiderInfo()).getRiderAccountId();
                    }


                    riderDeliveryOrderSyncOutClient.asyncOut(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.TRANS_TO_THIRD_PART_DELIVERY.getValue(),
                            new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderBizType(),
                                    deliveryOrder.getOrderId(), deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
                            new DeliveryChangeSyncOutMessage.TransToThirdPartDeliveryBody(oldRiderAccountId)));
                }
                break;
            case MERCHANT_SELF_DELIVERY:
                //判断转单后的配送状态
                DeliveryStatusEnum newDeliveryStatus = getDeliveryOrderStatusOfNewDeliveryOrder(orderInfo);

                //创建商家自配送运单
                deliveryOrder = DeliveryOrder.createMerchantDeliveryOrder(orderInfo, newDeliveryStatus);
                deliveryOrder.setTransType(transType);

                RouteInfoDTO routeInfoDTO = null;
                Optional<RouteInfoDTO> routeInfoDTOOpt = routeFacade.queryRideRouteInfo4DrunkHorse(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getReceiver().getReceiverAddress().getCoordinatePoint());
                if (routeInfoDTOOpt.isPresent()) {
                    routeInfoDTO = routeInfoDTOOpt.get();
                    deliveryOrder.setDistance(Optional.ofNullable(routeInfoDTO.getDistance()).map(Double::longValue).orElse(0L));
                    if (com.sankuai.meituan.shangou.dms.base.utils.MccConfigUtils.getFusionGaryV2(deliveryOrder.getTenantId(), deliveryOrder.getStoreId())) {
                        DeliveryDimensionPoi deliveryDimensionPoi = deliveryDimensionPoiRepository.queryDeliveryDimensionPoi(deliveryOrder.getTenantId(), deliveryOrder.getStoreId());
                        assessTimeAppender.appendAssesDeliveryDuration(deliveryDimensionPoi, orderInfo, deliveryOrder, new Result<Double>(Optional.ofNullable(routeInfoDTOOpt.get().getDistance()).orElse(0d)));
                    } else {
                        assessTimeAppender.appendAssesDeliveryDuration(orderInfo, deliveryOrder, new Result<Double>(Optional.ofNullable(routeInfoDTOOpt.get().getDistance()).orElse(0d)));
                    }
                }

                if(currentDeliveryOrder!=null && currentDeliveryOrder.getPlatformSourceEnum() == PlatformSourceEnum.OFC){
                    deliveryOrder.setPlatformSourceEnum(PlatformSourceEnum.OFC);
                    deliveryOrder.setStoreId(currentDeliveryOrder.getStoreId());
                    deliveryOrder.setFulfillmentOrderId(currentDeliveryOrder.getFulfillmentOrderId());
                    deliveryOrder.setOrderKey(new OrderKey(currentDeliveryOrder.getTenantId(),currentDeliveryOrder.getStoreId(),currentDeliveryOrder.getOrderId()));
                }

                //查询原来是拣配一体的还是拣配分离的
                Optional<DeliveryOrder> historySelfDeliveryOrderOpt = deliveryOrders.stream()
                        .filter(historyDeliveryOrder -> Objects.equals(historyDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode()))
                        .findFirst();
                if (historySelfDeliveryOrderOpt.isPresent()) {
                    deliveryOrder.setPickDeliverySplitTag(historySelfDeliveryOrderOpt.get().getPickDeliverySplitTag());
                }else {
                    int splitType =ofcRemoteService.queryPickDeliveryWorkMode(deliveryOrder.getTenantId(), deliveryOrder.getStoreId());
                    if(Objects.equals(splitType, PickDeliveryWorkModeEnum.PICK_DELIVERY_SPLIT.getCode())){
                        deliveryOrder.setPickDeliverySplitTag(true);
                    }else {
                        deliveryOrder.setPickDeliverySplitTag(false);
                    }
                }

                deliveryOrder.activate();
                deliveryOrderRepository.save(deliveryOrder);

                if(StringUtils.isEmpty(msg.getRiderName()) && StringUtils.isEmpty(msg.getRiderPhone())){
                    merchantSelfDeliveryPlatformClient.launch(opDeliveryPoi.get(),orderInfo,deliveryOrder,transferOrderMarkEnum.getCode());
                }else {
                    deliveryOrderRepository.updateRiderInfo(deliveryOrder.getId(), new StaffRider(msg.getRiderName(), msg.getRiderPhone(), null, msg.getRiderAccountId()),deliveryOrder.getTenantId(),deliveryOrder.getStoreId());
                }


                savePricingRouteInfo(deliveryOrder, routeInfoDTO);

                //延迟通知订单
                delayNotifyDeliveryStatusMessageProducer.sendDelayMessageInMillis(
                        new DelayNotifyDeliveryStatusMessage(deliveryOrder.getId(), deliveryOrder.getStatus(), msg.getRiderName(),deliveryOrder.getTenantId(),deliveryOrder.getStoreId()),
                        MccConfigUtils.getDelayNotifyDuration());


                //发MQ通知
                if(MccConfigUtils.checkIsDHTenant(msg.getTenantId())){
                    deliveryChangeNotifyService.notifyDrunkHorseTransDeliveryType(orderInfo.getChannelOrderId(),
                            orderInfo.getOrderBizType(), TransDeliveryTypeOperationTypeEnum.TURN_TO_SELF_DELIVERY, msg.getRiderAccountId(), msg.getRiderName());
                }

                riderDeliveryOrderSyncOutClient.asyncOut(new DeliveryChangeSyncOutMessage(DeliveryAsyncOutTypeEnum.TRANS_TO_SELF_DELIVERY.getValue(),
                        new DeliveryChangeSyncOutMessage.Head(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getId(), deliveryOrder.getOrderBizType(),
                                deliveryOrder.getOrderId(), deliveryOrder.getChannelOrderId(), deliveryOrder.getStatus().getCode()),
                        new DeliveryChangeSyncOutMessage.TransToSelfDeliveryBody(msg.getRiderAccountId())));

                //歪马微商城同步TSP转自配消息
                if (Objects.equals(orderInfo.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
                    syncDeliveryPlatformChange(deliveryOrder, DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY);
                }
                break;
            default:
                break;
        }
        if (!isMedicineUW) {
            deliveryTransformApplicationService.notifyOrderTransfer(deliveryOrder, lastDeliveryPlatform);
            deliveryTransformApplicationService.sendTransferTrack(deliveryOrder, transferOperate);
        }
        return CONSUME_SUCCESS;
    }

    private void syncDeliveryPlatformChange(DeliveryOrder deliveryOrder, DeliveryPlatformEnum deliveryPlatformEnum) {
        try {
            RetryTemplateUtil.simpleWithFixedRetry(
                    3,
                    100
            ).execute(context -> {
                ocmsChannelClient.syncDeliveryPlatformChange(deliveryOrder.getTenantId(), deliveryOrder.getStoreId(), deliveryOrder.getChannelOrderId(), deliveryOrder.getOrderBizType(), deliveryPlatformEnum);
                return null;
            });
        } catch (Exception e) {
            log.error("ocmsChannelClient.syncDeliveryPlatformChange error", e);
            Cat.logEvent("SYNC_OPEN_ERROR", "SYNC_PLATFORM_CHANGE");
        }
    }

    //歪马 && 当前运单是三方 && 当前运单状态为0
    private static boolean noNeedToCancel(DeliveryOrder currentDeliveryOrder) {
        try {
            boolean isThirdDelivery = !Objects.equals(currentDeliveryOrder.getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode());
            boolean isInitStatus = Objects.equals(currentDeliveryOrder.getStatus(), DeliveryStatusEnum.INIT);
            return isThirdDelivery && isInitStatus;
        } catch (Exception e) {
            log.error("noNeedToCancel error", e);
            return false;
        }
    }

    private DeliveryTransSelfMsg translateMessage(MafkaMessage mafkaMessage) {
        try {
            DeliveryTransSelfMsg message = translateMessage(mafkaMessage, DeliveryTransSelfMsg.class);
            Preconditions.checkNotNull(message, "empty mafkaMessage");
            return message;

        } catch (Exception e) {
            log.error("解析消息失败:{}", mafkaMessage, e);
            return null;
        }
    }

    /**
     * 未拣货完成 ==> 骑手已接单
     * 拣货完成 ==> 骑手已取货
     */
    private DeliveryStatusEnum getDeliveryOrderStatusOfNewDeliveryOrder(OrderInfo orderInfo) {
        LocalDateTime pickCompleteTime = orderInfo.getPickCompleteTime();
        if (Objects.isNull(pickCompleteTime) || pickCompleteTime.isAfter(LocalDateTime.now())) {
            return DeliveryStatusEnum.RIDER_ASSIGNED;
        } else {
            return DeliveryStatusEnum.RIDER_TAKEN_GOODS;
        }
    }

    /**
     * 是否为平台配送转自送/聚合配送，单独处理lastDeliveryPlatform值，返回true代表需要处理
     */
    private boolean isProcessLastDeliveryPlatform4OrderPlatformDelivery(OrderInfo orderInfo, DeliveryTransSelfMsg msg) {

        DynamicOrderBizType orderBizTypeEnum = DynamicOrderBizType.findOf(orderInfo.getOrderBizType());
        if (Objects.isNull(orderBizTypeEnum)) {
            return false;
        }

        if (!DynamicOrderBizType.DOU_YIN.equals(orderBizTypeEnum) && !DynamicOrderBizType.YOU_ZAN_MIDDLE.equals(orderBizTypeEnum)) {
            return false;
        }

        if (Objects.isNull(msg.getDeliveryPlatformCode())) {
            return false;
        }

        return Objects.equals(msg.getDeliveryPlatformCode(), DeliveryPlatformEnum.SELF_BUILT_DELIVERY_PLATFORM.getCode())
                || Objects.equals(msg.getDeliveryPlatformCode(), DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
    }


    private void savePricingRouteInfo(DeliveryOrder deliveryOrder, RouteInfoDTO routeInfoDTO) {
        try {
            pricingRouteInfoRepository.save(buildPricingRouteInfoDO(deliveryOrder, routeInfoDTO));
        } catch (Exception e) {
            log.error("保存定价路线信息失败", e);
            Cat.logEvent("NAVIGATE", "PERSIST_ROUTE_INFO_ERROR");
        }
    }

    private PricingRouteInfoDO buildPricingRouteInfoDO(DeliveryOrder deliveryOrder, RouteInfoDTO routeInfoDTO) {
        PricingRouteInfoDO pricingRouteInfoDO = new PricingRouteInfoDO();
        pricingRouteInfoDO.setDistance(routeInfoDTO.getDistance().longValue());
        pricingRouteInfoDO.setRouteId(routeInfoDTO.getRouteId());
        pricingRouteInfoDO.setPolyline(routeInfoDTO.getPolyline());
        pricingRouteInfoDO.setTenantId(deliveryOrder.getTenantId());
        pricingRouteInfoDO.setStoreId(deliveryOrder.getStoreId());
        pricingRouteInfoDO.setDeliveryOrderId(deliveryOrder.getId());
        pricingRouteInfoDO.setOrigin(Optional.ofNullable(routeInfoDTO.getOrigin())
                .map(origin -> routeInfoDTO.getOrigin().getLongitude() + "," + routeInfoDTO.getOrigin().getLatitude())
                .orElse(null));
        pricingRouteInfoDO.setDestination(Optional.ofNullable(routeInfoDTO.getDestination())
                .map(origin -> routeInfoDTO.getDestination().getLongitude() + "," + routeInfoDTO.getDestination().getLatitude())
                .orElse(null));
        pricingRouteInfoDO.setDuration(Optional.ofNullable(routeInfoDTO.getDuration()).map(Double::longValue).orElse(null));

        return pricingRouteInfoDO;
    }
}
