package com.sankuai.shangou.logistics.delivery.configure;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.logistics.delivery.configure.value.interval.IntervalTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 门店配置明细VO
 *
 * <AUTHOR>
 * @date 2025-07-04
 * @email <EMAIL>
 */
@Data
public class DeliveryConfigDetailVO {

    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "门店ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "配送平台配置列表",
            requiredness = Requiredness.OPTIONAL
    )
    private List<DeliveryPlatformConfigVO> deliveryPlatformConfig;

    @FieldDoc(
            description = "自送配置",
            requiredness = Requiredness.OPTIONAL
    )
    private SelfDeliveryConfigVO selfDeliveryConfig;

    // ========== 内部类定义 ==========

    /**
     * 配送平台配置
     */
    @Data
    public static class DeliveryPlatformConfigVO {
        @FieldDoc(description = "渠道类型")
        private Integer channelType;

        @FieldDoc(description = "平台编码")
        private Integer platformCode;

        @FieldDoc(description = "状态")
        private Integer status;

        @FieldDoc(description = "配送发起点")
        private Integer deliveryLaunchPoint;

        @FieldDoc(description = "跳转链接")
        private String redirectUrl;

        @FieldDoc(description = "配送发起延迟分钟数")
        private Integer deliveryLaunchDelayMinutes;

        @FieldDoc(description = "预约单配送发起分钟数")
        private Integer bookingOrderDeliveryLaunchMinutes;

        @FieldDoc(description = "二级配送配置")
        private SecondDeliveryConfigVO secondDeliveryConfig;

        @FieldDoc(description = "自送预约配送规则")
        private SelfDeliveryBookingDeliveryRuleVO selfDeliveryBookingDeliveryRule;
    }

    /**
     * 二级配送配置
     */
    @Data
    public static class SecondDeliveryConfigVO {
        @FieldDoc(description = "二级平台编码")
        private List<Integer> secondPlatformCode;

        @FieldDoc(description = "跳转链接")
        private String redirectUrl;

        @FieldDoc(description = "禁用条件")
        private ForbiddenConditionVO forbiddenCondition;
    }

    /**
     * 禁用条件
     */
    @Data
    public static class ForbiddenConditionVO {
        @FieldDoc(description = "订单标签")
        private List<Long> orderTags;

        @FieldDoc(description = "订单实际支付金额")
        private String orderActualPayment;
    }

    /**
     * 自送预约配送规则
     */
    @Data
    public static class SelfDeliveryBookingDeliveryRuleVO {
        @FieldDoc(description = "是否需要营业时间下推")
        private Boolean needBusinessHoursPushdown;

        @FieldDoc(description = "预约下推时间配置")
        private List<BookingPushDownTimeConfigVO> bookingPushDownTimeConfig;
    }

    /**
     * 预约下推时间配置
     */
    @Data
    public static class BookingPushDownTimeConfigVO {
        @FieldDoc(description = "订单标签")
        private List<Integer> orderTags;

        @FieldDoc(description = "条件")
        private List<ConditionVO> condition;
    }

    /**
     * 条件
     */
    @Data
    public static class ConditionVO {
        @FieldDoc(description = "标识符")
        private String identifer;

        @FieldDoc(description = "区间")
        private IntervalVO interval;

        @FieldDoc(description = "值")
        private String value;
    }

    /**
     * 区间
     */
    @Data
    public static class IntervalVO {
        @FieldDoc(description = "值")
        private List<String> values;

        @FieldDoc(description = "区间类型")
        private Integer intervalType = IntervalTypeEnum.RIGHT_OPEN.getType();
    }

    /**
     * 自送配置
     */
    @Data
    public static class SelfDeliveryConfigVO {
        @FieldDoc(description = "考核时间")
        private List<AssertTimeVO> assertTime;

        @FieldDoc(description = "拣配作业模式")
        private Integer pickDeliveryWorkMode;

        @FieldDoc(description = "内部导航模式")
        private Integer internalNavigationMode;

        @FieldDoc(description = "已完成排序模式")
        private Integer completedSortMode;

        @FieldDoc(description = "骑手转换角色")
        private List<Long> riderTransRoles;

        @FieldDoc(description = "配送完成模式")
        private DeliveryCompleteModeVO deliveryCompleteMode;

        @FieldDoc(description = "配送提醒配置")
        private DeliveryRemindConfigVO deliveryRemindConfig;
    }

    /**
     * 考核时间
     */
    @Data
    public static class AssertTimeVO {
        @FieldDoc(description = "类型")
        private Integer type;

        @FieldDoc(description = "提示")
        private Integer hintType;

        @FieldDoc(description = "时长设置类型，1-固定时长 2-eta+-分钟")
        private Integer timeConfigType;

        @FieldDoc(description = "订单标签")
        private List<Integer> orderTags;

        @FieldDoc(description = "条件")
        private List<ConditionVO> condition;

    }



    /**
     * 确认送达操作配置
     */
    @Data
    public static class DeliveryCompleteModeVO {
        @FieldDoc(description = "距离提醒：0-不提示 1-提示")
        private Integer distanceReminder;

        @FieldDoc(description = "签收方式开关：0-关闭 1-开启")
        private Integer signTypeSwitch;

        @FieldDoc(description = "配送完成配置")
        private DeliveryCompleteConfigVO deliveryCompleteConfig;
    }

    /**
     * 配送完成配置
     */
    @Data
    public static class DeliveryCompleteConfigVO {

        @FieldDoc(description = "经营类型：1-直营 2-加盟 3-赋能")
        private Integer operationMode;

        @FieldDoc(description = "所有示例图片信息列表")
        private List<ExamplePicInfoVO> allExamplePicInfoList;

        @FieldDoc(description = "特殊商品上传图片配置")
        private List<SpecialProductUploadPicConfigVO> specialProductUploadPicConfig;

        @FieldDoc(description = "发送图片给顾客提示")
        private String sendPicToCustomerTips;

        @FieldDoc(description = "未联系顾客提示")
        private String notContactCustomerTips;

        @FieldDoc(description = "上传图片最大时长阈值")
        private Integer uploadImageDurationThreshold;

        @FieldDoc(description = "是否显示未联系顾客提示")
        private Boolean isShowNotContactCustomerTips;
    }

    /**
     * 示例图片信息
     */
    @Data
    public static class ExamplePicInfoVO {
        @FieldDoc(description = "图片类型")
        private Integer type;

        @FieldDoc(description = "图片名称")
        private String name;

        @FieldDoc(description = "图片链接")
        private String picUrl;

        @FieldDoc(description = "显示顺序")
        private Integer order;
    }

    /**
     * 特殊商品上传图片配置
     */
    @Data
    public static class SpecialProductUploadPicConfigVO {
        @FieldDoc(description = "商品类型")
        private Integer productType;

        @FieldDoc(description = "图片类型列表")
        private List<Integer> picTypeList;

        @FieldDoc(description = "是否强制上传图片")
        private Boolean isForceUploadPic;

        @FieldDoc(description = "需要上传的照片张数")
        private Integer needUploadPicCount;
    }

    /**
     * 配送提醒设置
     */
    @Data
    public static class DeliveryRemindConfigVO {
        @FieldDoc(description = "领取超时时间（分钟）")
        private Integer receiveTimeOutMins;

        @FieldDoc(description = "即将超时时间（分钟）")
        private Integer soonDeliveryTimeoutMinsBeforeEta;
    }

    /**
     * 配送超时配置
     */
    @Data
    public static class DeliveryTimeOutsBeforeEtaConfigVO {
        @FieldDoc(description = "立即单配送超时时间（分钟）")
        private Integer immediateBeforeEtaMins;

        @FieldDoc(description = "预约单配送超时时间（分钟）")
        private Integer bookingBeforeEtaMins;
    }
}
