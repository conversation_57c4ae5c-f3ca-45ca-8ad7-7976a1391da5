package com.sankuai.meituan.shangou.empower.tms.delivery.access.utils;

import com.dianping.cat.Cat;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.application.AssessTimeApplicationService;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.DeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.OrderInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.enums.AssessTimeConfigHintType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.enums.AssessTimeConfigType;
import com.sankuai.meituan.shangou.empower.tms.delivery.access.domain.enums.RewardHintEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.DeliveryDimensionPoi;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.domain.Result;
import com.sankuai.meituan.shangou.empower.tms.delivery.poi.utils.LionConfigUtils;
import com.sankuai.meituan.shangou.empower.tms.delivery.rider.domain.client.config.SdmsStoreConfigServiceClient;
import com.sankuai.shangou.logistics.delivery.configure.value.AssessTimeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024-10-12
 * @email <EMAIL>
 */
@Slf4j
@Service
public class AssessTimeAppender {

    @Resource
    private SdmsStoreConfigServiceClient sdmsStoreConfigServiceClient;

    @Resource
    private AssessTimeApplicationService assessTimeApplicationService;

    @Deprecated
    public void appendAssesDeliveryDuration(OrderInfo orderInfo, DeliveryOrder deliveryOrder, Result<Double> distanceResult) {
        if (MccConfigUtils.checkIsDHTenant(deliveryOrder.getTenantId())) {
            try {
                //非预定单才是这个逻辑
                if (!orderInfo.isBookingOrder()) {
                    if (MccConfigUtils.isAssessTimeBySecondStore(deliveryOrder.getStoreId())) {
                        Optional<Integer> assessDeliveryDurationOpt = sdmsStoreConfigServiceClient.calcAssessDeliveryDurationWithSeconds(
                                deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
                                deliveryOrder.getChannelOrderId(), deliveryOrder.getOrderBizType(),
                                distanceResult.getInfo().longValue()
                        );
                        assessDeliveryDurationOpt.ifPresent(durationSeconds -> {
                            deliveryOrder.setAssessDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getPayTime()) + durationSeconds * 1000);
                        });
                    } else {
                        Optional<Integer> assessDeliveryDurationOpt = sdmsStoreConfigServiceClient.calcAssessDeliveryDuration(
                                deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
                                deliveryOrder.getChannelOrderId(), deliveryOrder.getOrderBizType(),
                                distanceResult.getInfo().longValue()
                        );
                        assessDeliveryDurationOpt.ifPresent(durationMinus -> {
                            deliveryOrder.setAssessDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getPayTime()) + durationMinus * 60 * 1000);
                        });
                    }
                } else {
                    //预定单等于eta + x分钟
                    deliveryOrder.setAssessDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryTime().plusMinutes(LionConfigUtils.getPreOrderAssessAddMinus())));
                }
            } catch (Exception e) {
                log.error("calcAssessDeliveryDuration error", e);
                Cat.logEvent("ASSESS_TIME", "CALC_ERROR");
            }
        }
    }

    public void appendAssesDeliveryDuration(DeliveryDimensionPoi deliveryDimensionPoi, OrderInfo orderInfo, DeliveryOrder deliveryOrder, Result<Double> distanceResult) {
        try {
            //非预定单才是这个逻辑
            if (!orderInfo.isBookingOrder()) {
                Pair<AssessTimeConfig, Integer> assessTimeResultPair = assessTimeApplicationService.calcAssessDeliveryDuration(
                        deliveryOrder.getTenantId(), deliveryOrder.getStoreId(),
                        orderInfo,
                        distanceResult.getInfo().longValue(),
                        deliveryDimensionPoi.getAssessTimeConfig()
                );
                if (Objects.nonNull(assessTimeResultPair) && assessTimeResultPair.getValue() > 0) {
                    deliveryOrder.setAssessDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getPayTime()) + assessTimeResultPair.getValue() * 1000);
                    setRewardHintEnum(deliveryOrder, assessTimeResultPair);
                }
            } else {
                //预定单等于eta + x分钟
                deliveryOrder.setAssessDeliveryTime(TimeUtil.toMilliSeconds(orderInfo.getEstimatedDeliveryEndTime().plusMinutes(LionConfigUtils.getPreOrderAssessAddMinus())));
            }
        } catch (Exception e) {
            log.error("calcAssessDeliveryDuration error", e);
            Cat.logEvent("ASSESS_TIME", "CALC_ERROR");
        }
    }

    private void setRewardHintEnum(DeliveryOrder deliveryOrder, Pair<AssessTimeConfig, Integer> assessTimeResultPair) {
        try {
            AssessTimeConfig assessTimeConfig = assessTimeResultPair.getKey();
            AssessTimeConfigType assessTimeConfigType = AssessTimeConfigType.valueOfType(assessTimeConfig.getType());
            AssessTimeConfigHintType assessTimeConfigHintType = AssessTimeConfigHintType.valueOfType(assessTimeConfig.getHintType());
            RewardHintEnum rewardHintEnum = RewardHintEnum.getByAssessTimeConfigType(assessTimeConfigType, assessTimeConfigHintType);
            if (Objects.nonNull(rewardHintEnum)) {
                deliveryOrder.setRewardType(rewardHintEnum.getCode());
            }
        } catch (Exception e) {
            log.error("setRewardType error", e);
        }
    }

}
